import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { faInfoCircle, faEllipsisV } from '@fortawesome/free-solid-svg-icons';
import { library } from '@fortawesome/fontawesome-svg-core';
import { MenuController, AlertController, PopoverController, ToastController, Platform } from '@ionic/angular';
import { HelpService } from '../services/help.service';
import { DataService } from '../services/data.service';
import { UnviredCordovaSDK, RequestType, ResultType, NotificationListenerType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstant } from '../../constants/appConstants';
import { HomePopupPage } from '../home-popup/home-popup.page';
import { TranslateService } from '@ngx-translate/core';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { HTTP } from '@awesome-cordova-plugins/http/ngx';
// import { FileTransfer, FileUploadOptions, FileTransferObject } from '@ionic-native/file-transfer/ngx';
import { Zip } from '@awesome-cordova-plugins/zip/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { AlertService } from '../services/alert.service';
import { UserPreferenceService } from '../services/user-preference.service';
import { VisionAIService } from '../services/vision-ai.service';
import { FileOpener } from '@awesome-cordova-plugins/file-opener/ngx';
import * as tf from '@tensorflow/tfjs';
library.add(faInfoCircle, faEllipsisV);
declare var cordova: any;
// Extend CordovaPlugins interface to include fileOpener2
declare global {
  interface CordovaPlugins {
    fileOpener2?: any;
  }
}


@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
})
export class HomePage {
  help = true;
  tooltipEvent: 'click' | 'press' = 'click';
  showArrow = true;
  duration = 3000;
  customizationAlert: any;
  refreshCalled: boolean = false;
  userPreferenceSet: boolean = false;
  // private fileTransfer: FileTransferObject;
  navigatedBack: boolean = false;
  showingProcessingAlert: boolean = false;
  imageurl: string = "../../assets/img/MainMenu_Inspections.png"
  selectedIndustry: any;

  constructor(private route: Router,private fileOpener2: FileOpener , public userPreferenceService: UserPreferenceService, public alertService: AlertService,private nativeHTTP: HTTP, public device: Device, public file: File, public menu: MenuController, public platform: Platform, public helpService: HelpService, public dataService: DataService, public unviredCordovaSdk: UnviredCordovaSDK, public alertController: AlertController, public popoverController: PopoverController, public translate: TranslateService, public toastController: ToastController, private zip: Zip,
    private visionAIService: VisionAIService) {
    // this.fileTransfer = this.transfer.create();
  }

  async ngOnInit() {
    if(this.device.platform == 'browser')  {

    } else {
      this.getMessageCycle()
      setTimeout(async () => {
        if (this.dataService.firstLogIn == false) {
          let result = await this.unviredCordovaSdk.dbSelect("DWNLD_TIME_HEADER", "")
            if (result.type == ResultType.success) {
              if (result.data.length > 0) {
                console.log(this.dataService.getNetworkStatus());
                console.log(this.dataService.refreshCalled);
                if (this.dataService.refreshCalled == false && this.dataService.getNetworkStatus() == true) {
                  var called = await this.unviredCordovaSdk.isInOutBox(AppConstant.PA_ROPE_INSPECTIONS_PA_REFRESH_CUSTOMIZATION)
                  if (called.type == ResultType.success) {
                    if (called.data == false || called.data == 'false') {
                      var temp = await this.unviredCordovaSdk.syncBackground(RequestType.QUERY, "", "", AppConstant.PA_ROPE_INSPECTIONS_PA_REFRESH_CUSTOMIZATION, "", "", true)
                      this.dataService.refreshCalled = true;
                      if (temp.type == ResultType.success) {
                        // console.log("result ++ " + JSON.stringify(temp))
                        // this.unviredCordovaSdk.logDebug("home", "checkAndDownloadData", "result ++ " + JSON.stringify(temp))
                        if(navigator.onLine) {
                          this.unviredCordovaSdk.logInfo("HOME","ngOnInit","calling checkAndDownloadLatestMlModel on Home page initialization")
                          await this.alertService.present('custom-loader');
                          await this.checkAndDownloadLatestMlModel();
                          if(this.alertService.isLoading) {
                            await this.alertService.dismiss();
                          }
                        }
                      } else {
                        this.showAlert("Error", temp.message)
                      }
                    }
                  }
                } else {
                  this.dataService.refreshCalled = true;
                  if(this.dataService.selectedRole == 'Employee') {
                      await this.alertService.present('custom-loader');
                      await this.checkAndDownloadLatestMlModel();
                      if(this.alertService.isLoading) {
                        await this.alertService.dismiss();
                      }
                    }
                }
              } else {
                await this.checkAndDownloadData();
              }
            } //else {
            //   await this.checkAndDownloadData();
            // }
          // })
        }
        var showPreferenceDialogue = await this.userPreferenceService.getUserPreference('showPreferenceDialogue');
        if(showPreferenceDialogue != '' && showPreferenceDialogue != 'false') {
          await this.userPreferenceService.dbInsertOrUpdate('showPreferenceDialogue', 'false');
          if(this.device.platform == 'Android') {
            this.showResourceDownloadAlert();
          } else {
            this.showUserPreferenceAlert();
          }
        }
      }, 400);
      this.dataService.getApplicationSetting()
    }
    await this.dataService.userEnabledForInsightAI();
    //! check if any pending or failed quick inspections are present, if any then submit them again
    if(this.dataService.isUserEnabledForInsightAI) {
      await this.visionAIService.submitPendingQuickInspections();
    }
  }

  async listAndRemove() {
    await this.alertService.present('custom-loader');
    // console.log(await tf.io.listModels());
    let models:any = await tf.io.listModels();
    for (let modelPath of Object.keys(models)) {
      await tf.io.removeModel(modelPath);
    }
    // console.log(await tf.io.listModels());
    if(this.alertService.isLoading) {
      await this.alertService.dismiss();
    }
  }

  async checkAndDownloadLatestMlModel() {
    this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","checkAndDownloadLatestMlModel started")
    console.group("checkAndDownloadLatestMlModel")
    console.log("this method is to check and download latest Ml model"); // * check and download the latest model from server
    this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","checking user is enabled for Insight AI")
    await this.dataService.userEnabledForInsightAI();
    this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","is enabled for Insight AI completed")
      if(navigator.onLine) {
        console.log("device is online,continue to check and download latest model")
        this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","device is online,continue to check for the latest model and download it")
        if(this.dataService.isUserEnabledForInsightAI) {
          console.log("User is enabled for insightAI so continue to download the model")
          this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","User is enabled for insightAI,continue to download the model")
          let AIHeader;
          // if((this.device.platform!='browser' && !this.dataService.firstLogIn) || (this.device.platform=='browser' && !this.dataService.browserFirstLogIn)) {
            let modelRes = await this.unviredCordovaSdk.dbSelect("AI_MODEL_VERSION_HEADER",'')
            if(modelRes.type==ResultType.success) {
              this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","successful reading the AI_MODEL_VERSION_HEADER table from db");
              AIHeader = modelRes.data;
            } else if(modelRes.type==ResultType.error) {
              if(this.alertService.isLoading) {
                await this.alertService.dismiss()
              }
              this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","error in reading AI_MODEL_VERSION_HEADER table from db:"+modelRes.error);
              console.log("error reading AI_MODEL_VERSION_HEADER table in db",modelRes.error);
            }
          // }
          
          for(let i=0;i<AIHeader.length;i++) {
            let AI_MODEL_OBJ = {
              "AI_MODEL_VERSION_HEADER" : AIHeader[i]
            }

            this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel",`calling ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION api to check latest AI model ${AIHeader[i].MODEL_TYPE}`);
            let modelPares = await this.unviredCordovaSdk.syncForeground(RequestType.RQST, AI_MODEL_OBJ,"" , AppConstant.ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION, true)
            if(modelPares.type==ResultType.success){ //! ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION success
              this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","recieved response for ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION api call");
              console.log(modelPares);
              let modelRes = await this.unviredCordovaSdk.dbSelect("AI_MODEL_VERSION_HEADER",`MODEL_TYPE='${AIHeader[i].MODEL_TYPE}'`);
              if(modelRes.type==ResultType.success) {
                this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","successful reading the AI_MODEL_VERSION_HEADER from db after ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION api call");
                let resAIHeader = modelRes.data[0];
                if((!this.dataService.firstLogIn || (this.device.platform=='browser' && !this.dataService.browserFirstLogIn)) && resAIHeader.MODEL_NAME===AIHeader[i].MODEL_NAME) {
                  this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","no change in the model, the device has the latest model");
                  let modelExists: any;
                  this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","checking if the model is present in indexDB or not, event though there's no change in the model version");
                  modelExists = await this.visionAIService.checkModel(AIHeader[i].MODEL_TYPE)
                  if(!modelExists) {
                    this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","model is not present, calling the downloadModelFromServer method to download model from server");
                    await this.visionAIService.downloadModelFromServer(AIHeader[i].MODEL_TYPE);
                    await this.dataService.userEnabledForInsightAI(); // ! dont't do it multiple times, do it only once after customisation download
                  } else {
                    this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","AI model present in the indexDB, no need to download it again");
                  }
                } else {
                  console.log(this.dataService.browserFirstLogIn)
                  this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","calling the downloadModelFromServer method to download latest model from server");
                  await this.visionAIService.downloadModelFromServer(AIHeader[i].MODEL_TYPE);
                  await this.dataService.userEnabledForInsightAI();
                }
              } else if(modelRes.type==ResultType.error){
                this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","error reading the AI_MODEL_VERSION_HEADER table from db after ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION api call:"+modelRes.error);
                console.log("error reading AI model heaser db")
              }
            } else if(modelPares.type==ResultType.error){ //! ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION failure
              this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","error calling ROPE_INSPECTIONS_PA_CHECK_AI_MODEL_VERSION api:"+modelPares.error);
              await this.alertService.showAlert("Error!",modelPares.error);
            }
          }
        }
      } else {
        this.unviredCordovaSdk.logInfo("HOME","checkAndDownloadLatestMlModel","device is offline so continue to use the existing model");
      }
      // if(this.alertService.isLoading) {
      //   await this.alertService.dismiss();
      // }
  }

  async checkAndsetHomeScreenImages() {
    await this.dataService.setInitialImages()
  }

  getMessageCycle() {
    if(this.device.platform == 'windows') {
      this.dataService.refreshDataWindows();
    }
  }

  // navigate to Inspection Home
  async inspection() {
    this.route.navigate(['detailed-routine-inspection']);
    // this.selectedIndustry = await this.userPreferenceService.getUserPreference("industry")
    // this.selectedIndustry = this.selectedIndustry != '' ? JSON.parse(this.selectedIndustry) : '';
    // if (this.selectedIndustry == '' || this.selectedIndustry == undefined || this.selectedIndustry == null) {
    //   this.dataService.setConfigurationOption()
    //   this.dataService.setLastUsedAccount();
    //   this.dataService.setLastUsedAsset();
    //   this.dataService.setLastUsedWorkOrder();
    //   this.route.navigate(['inspection-home']);
    // } else if (this.selectedIndustry.NAME == 'Vessel Mooring') {
    //   this.route.navigate(['detailed-routine-inspection']);
    // } else {
    //   this.dataService.setConfigurationOption()
    //   this.dataService.setLastUsedAccount();
    //   this.dataService.setLastUsedAsset();
    //   this.dataService.setLastUsedWorkOrder();
    //   this.route.navigate(['inspection-home']);
    // }
  }

  //navigate to line tracker
  lineTracker() {
    this.dataService.setConfigurationOption()
    this.dataService.setLastUsedAccount();
    this.dataService.setLastUsedAsset();
    this.dataService.setLastUsedWorkOrder();
    this.dataService.navigateToLineTracker(this)
  }

  switchMode() {
    this.help = !this.help;
    if (!this.help) {
      this.duration = 0;
      this.menu.enable(false, 'helpMenu');
    } else {
      this.duration = 3000;
      this.menu.enable(true, 'helpMenu');
    }
  }

  // navigate to Contact Page
  contact() {
    this.dataService.gotoContact();
  }

  agree() {
    this.route.navigate(['agreement']);
  }

  // navigate to Resources Page
  resource() {
    if(this.device.platform != 'browser') {
      this.route.navigate(['resource']);
    } else {
      this.dataService.gotoResources();
    }
  }

  //Exit help mode
  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  async ionViewWillEnter() {
    if(this.device.platform == 'browser') {      
      if(this.dataService.browserFirstLogIn == true) {
        this.checkAndDownloadDataForBrowser()      
      } else {  
      this.processBrowserData();      
      }
    } else {
      this.ionViewWillEnterProcesDataMobile();
    }
    await this.checkAndsetHomeScreenImages()
  }

  processBrowserData() {
    setTimeout(() => {
      this.unviredCordovaSdk.dbSelect("DWNLD_TIME_HEADER", "").then(async (result) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            if (this.dataService.refreshCalled == false) {
              // await this.alertService.present()
                  var temp = await this.unviredCordovaSdk.syncForeground(RequestType.QUERY, "", "", AppConstant.PA_ROPE_INSPECTIONS_PA_REFRESH_CUSTOMIZATION, true);
                  this.dataService.refreshCalled = true;
                  this.alertService.dismiss();     
                  if (temp.type == ResultType.success) {
                    // console.log("result ++ " + JSON.stringify(temp))
                    // this.unviredCordovaSdk.logDebug("home", "checkAndDownloadData", "result ++ " + JSON.stringify(temp))
                    await this.alertService.present('custom-loader');
                    await this.checkAndDownloadLatestMlModel();
                    if(this.alertService.isLoading) {
                      await this.alertService.dismiss();
                    }
                  } else {
                    this.route.navigate(['browser-home'], {replaceUrl: true})
                    this.alertService.dismiss();   
                    // this.showAlert("Error", temp.message)
                  } 
                            
            } else {
              this.dataService.refreshCalled = true;
            }
          } else {
            await this.checkAndDownloadData();
          }
        } 
        console.log(localStorage.getItem("ROPE_INSPECTIONS_token"));
        if(localStorage.getItem('ROPE_INSPECTIONS_token') != '' && localStorage.getItem('ROPE_INSPECTIONS_token') != null && localStorage.getItem('ROPE_INSPECTIONS_token') != undefined ) {     
          if(this.dataService.redirectPage != '') {
            switch (this.dataService.redirectPage) {
              case "inspections":
                this.inspection();
                this.dataService.redirectPage = ''
                break;
              case "lineTracker":
                this.lineTracker();
                this.dataService.redirectPage = ''
                break;
            }
          }
        }        
      })
      this.dataService.showSaveDB = true;
    }, 500);  
  }

  async ionViewWillEnterProcesDataMobile() {
    console.log(" home page : First login " + this.dataService.firstLogIn)
    this.unviredCordovaSdk.getMessages();
    this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", " home page : First login " + this.dataService.firstLogIn)
    setTimeout(async () => {
      if (this.dataService.firstLogIn == true) {
        var inBoxCount = await this.unviredCordovaSdk.inBoxItemCount()
        if(parseInt(inBoxCount) == 0) {
          this.showCustomizationAlert(true);
        } else {
          this.showProcessingAlert()
        }
      } else {
        this.checkAndDownloadData()
      }
    }, 200);
    this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", "register notif listener")
    console.log("register notif listener")

    this.unviredCordovaSdk.registerNotifListener().subscribe(async (result) => {
      this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", JSON.stringify(result));
      switch (result.type) {
        case NotificationListenerType.dataReceived:
          // this.dataService.clearRefreshDownloadComplete();
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "DATA RECIVED");
          break;
        case NotificationListenerType.dataChanged:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "DATA CHANGED");
          if(this.device.platform=='windows') {
            if(result.data.length>0 && result.data[0].CASE_ID!="" && result.data[0].CASE_ID!=null) {
              this.unviredCordovaSdk.logDebug("HOME", "dataChanged", "dataChanged" + result.data[0]);
              await this.presentToastNotif(`Message has been sent. Case created with Id:${result.data[0].CASE_ID}`);
            }
          } else if(this.device.platform =='iOS') {
            if(result.data.length>0 && result.data[0].fields.CASE_ID!="" && result.data[0].fields.CASE_ID!=null) {
              this.unviredCordovaSdk.logDebug("HOME", "dataChanged", "dataChanged" + result.data[0]);
            await this.presentToastNotif(`Message has been sent. Case created with Id:${result.data[0].fields.CASE_ID}`);
            }
          } else {
            if(result.data.length>0 && result.data[0].hasOwnProperty('INPUT_CREATE_CASE_HEADER') && result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID!=null && result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID!="")  {
              this.unviredCordovaSdk.logDebug("HOME", "dataChanged", "dataChanged" + result.data[0]);
              await this.presentToastNotif(`Message has been sent. Case created with Id:${result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID}`);
            }
          }
          break;
        case NotificationListenerType.dataSend:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "DATA SENT");
          break;
        case NotificationListenerType.appReset:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "APP RESET");
          this.refreshCalled = false;
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.attachmentDownloadSuccess:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "attachmentDownloadSuccess");
          this.refreshCalled = false;
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.attachmentDownloadError:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "attachmentDownloadError");
          this.refreshCalled = false;
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.incomingDataProcessingFinished:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "IMCOMING DATA PROCESSING FINISHED");
          if(this.device.platform=='windows') {
            if(result.data.length>0 && result.data[0].CASE_ID!="" && result.data[0].CASE_ID!=null) {
              this.unviredCordovaSdk.logDebug("HOME", "incomingDataProcessingFinished", "incomingDataProcessingFinished" + result.data[0]);
              await this.presentToastNotif(`Message has been sent. Case created with Id:${result.data[0].CASE_ID}`);
            }
          } else if(this.device.platform =='iOS') {
            if(result.data.length>0 && result.data[0].fields.CASE_ID!="" && result.data[0].fields.CASE_ID!=null) {
              this.unviredCordovaSdk.logDebug("HOME", "incomingDataProcessingFinished", "incomingDataProcessingFinished" + result.data[0]);
            await this.presentToastNotif(`Message has been sent. Case created with Id:${result.data[0].fields.CASE_ID}`);
            }
          } else {
            if(result.data.length>0 && result.data[0].hasOwnProperty('INPUT_CREATE_CASE_HEADER') && result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID!=null && result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID!="")  {
              this.unviredCordovaSdk.logDebug("HOME", "incomingDataProcessingFinished", "incomingDataProcessingFinished" + result.data[0]);
              await this.presentToastNotif(`Message has been sent. Case created with Id:${result.data[0].INPUT_CREATE_CASE_HEADER.CASE_ID}`);
            }
          }
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          // if(this.userPreferenceSet == false) {
          //   this.showUserPreferenceAlert();
          //   this.userPreferenceSet = true
          // }
          // this.showUserPreferenceAlert();
          // this.refreshCalled = false;
          // this.dataService.clearDataRefresh();
          // if(this.customizationAlert) {
          //   this.customizationAlert.dismiss();
          // }
          break;
        case NotificationListenerType.attachmentDownloadWaiting:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "attachmentDownloadWaiting");
          this.refreshCalled = false;
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.infoMessage:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "infoMessage");
          this.refreshCalled = false;
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          this.handleInfoMessage(result)

          break;
        case NotificationListenerType.serverError:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "serverError");
          this.refreshCalled = false;
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            // this.customizationAlert.dismiss();
            //commented to fix user preference popup on firstLogin
          }
          break;
        case NotificationListenerType.attachmentDownloadCompleted:
          this.unviredCordovaSdk.logInfo("HOME", "registerNotifListener", "attachmentDownloadCompleted");
          this.refreshCalled = false;
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.JWTTokenReceived:
          // saving the token in localstorage to make rest api call to fetch the model from hosted url
          localStorage.setItem('samsonToken',result.data);
          this.unviredCordovaSdk.logInfo("HOME","registerNotificationListener","JWT Token Recieved and saved to localstorage")
          break;
        default:
          this.dataService.clearDataRefresh();
          this.refreshCalled = false;
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
      }
    });
  }

  public handleInfoMessage(result) {

    this.unviredCordovaSdk.logInfo("Utility", "handleInfoMessage()", "Info Message:" + JSON.stringify(result, null, 2))

    if (!result.data || result.data.length == 0) {
      return
    }

    let messages: any = result.data
    if (!messages || messages.length == 0 || messages.some(msg=>msg?.message?.includes("QUICK_INSPECT"))) {
      return
    }

    let messageTitle: string = messages[0].CATEGORY
    if (!messageTitle) {
      messageTitle = "Information"
    }

    var messageString = ''
    messages.forEach(element => {
      if (element.MESSAGE) {
        messageString += element.MESSAGE + '<br>'
      }
    });

    this.showAlert(messageTitle.substring(0, 1).toUpperCase() + messageTitle.substr(1).toLowerCase(), messageString)
  }

  getMasterData(downloadHistoricalInspection) {
    console.log("making async call")
    this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", " making async call ")
    var inputJson = {"downloadHistoricalInsp":""}
    if(downloadHistoricalInspection == true) {
      inputJson.downloadHistoricalInsp = 'X'
    }
    if(this.dataService.selectedRole == 'Employee') {
      inputJson['accountId'] = this.dataService.lastUsedAccount.ID ? this.dataService.lastUsedAccount.ID : '';
      inputJson['assetId'] = this.dataService.lastUsedAsset.ID ? this.dataService.lastUsedAsset.ID : '';
    }
    this.unviredCordovaSdk.syncBackground(RequestType.PULL, "", inputJson, AppConstant.PA_ROPE_INSPECTION_PA_GET_CUSTOMIZATON, "", "", true).then(async (result) => {
      if (result.type == ResultType.success) {
        console.log("result ++ " + JSON.stringify(result))
        this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", "result ++ " + JSON.stringify(result))
        this.dataService.refreshCalled = true;
        await this.dataService.refreshData();
      }
    })
  }

  async getMasterDataForBrowser(downloadHistoricalInspection) {
    var inputJson = {"downloadHistoricalInsp":""}
    if(downloadHistoricalInspection == true) {
      inputJson.downloadHistoricalInsp = 'X'
    }
    var temp = await this.unviredCordovaSdk.syncForeground(RequestType.QUERY, "", inputJson, AppConstant.PA_ROPE_INSPECTION_PA_GET_CUSTOMIZATON, true)
        if(temp != undefined) {
          if (temp.type == ResultType.success) {
            if (this.customizationAlert) {
              this.customizationAlert.dismiss();
              await this.unviredCordovaSdk.dbSaveWebData();
              this.dataService.showSaveDB = true;
            }
          } else {
            this.route.navigate(['browser-home'], {replaceUrl: true})
            this.customizationAlert.dismiss();  
          }
        } else {
          this.route.navigate(['browser-home'], {replaceUrl: true})
          this.navigatedBack = true;
            this.customizationAlert.dismiss();         
        }
  }

  async getMasterDataMenu() {
    console.log("making async call")
    this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", " making async call ")
    if (this.refreshCalled == false) {
      if (this.dataService.isConnectedToNetwork = true) {
        var tempDW = await this.unviredCordovaSdk.dbSelect("DWNLD_TIME_HEADER", "")
        if(tempDW.type == ResultType.success) {
          if(tempDW.data != undefined) {
            if(tempDW.data.length > 0) {
              var temp = await this.unviredCordovaSdk.dbDelete("DWNLD_TIME_HEADER", "")
              if(temp.type == ResultType.success) {
                if(temp.data != undefined) {
                  if(temp.data.length > 0) {
                    this.showCustomizationAlert(false);
                  } else {
                    this.showCustomizationAlert(false);
                  }
                } else {
                  this.showCustomizationAlert(false);
                }
              } else {
                this.showCustomizationAlert(false);
              }    
            } else {
              this.showCustomizationAlert(true);
            }   
          } else {
            this.showCustomizationAlert(true);
          }
        } else {
          this.showCustomizationAlert(true);
        }
      } else {
        var tempToast = await this.toastController.create({
          message: this.translate.instant("Connect to internet to refresh master data"),
          duration: 2000,
          color: "dark",
          position: "middle"
        });
        tempToast.present();
      }
    }
    //   if(this.refreshCalled == false) {
    //     this.unviredCordovaSdk.syncBackground(RequestType.PULL, "", "", AppConstant.PA_ROPE_INSPECTION_PA_GET_CUSTOMIZATON, "", "", true).then((result) => {
    //       if(result.type == ResultType.success) {
    //         console.log("result ++ " + JSON.stringify(result))
    //         this.refreshCalled = true;
    //         this.unviredCordovaSdk.logDebug("home","ionViewWillEnter", "result ++ " + JSON.stringify(result))
    //         this.dataService.refreshData();
    //       }      
    //     })    
    // } else {
    //   this.presentToast()
    // }
  }

  async showProcessingAlert() {
    if(this.showingProcessingAlert != true) {
      this.showingProcessingAlert = true;
    this.customizationAlert = await this.alertController.create({
      header: 'Alert',
      message: '<div><div class="imageStyle"><img src="./assets/img/blue-hourglass.gif"></div>' + 'Please wait while we configure data. Do not exit this screen.</div>',
      backdropDismiss: false,
      cssClass: 'waitImage',
    });
    await this.customizationAlert.present();
    console.log("Download alert presented")
    this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", " Download alert presented ")    
    await this.customizationAlert.onDidDismiss().then((data) => {
      console.log("Download alert dismissed")
      this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", "Download alert dismissed")
      if(this.device.platform == "Android") {
        this.showResourceDownloadAlert()
      } else {
        if(this.navigatedBack == false) {
          this.showUserPreferenceAlert()
        }
      }
      this.dataService.firstLogIn = false;
    })
  }
  }

  async showCustomizationAlert(downloadHistoricalInspection) {
    this.customizationAlert = await this.alertController.create({
      header: 'Alert',
      message: '<div><div class="imageStyle"><img src="./assets/img/blue-hourglass.gif"></div>' + 'Please wait while we configure data. Do not exit this screen.</div>',
      backdropDismiss: false,
      cssClass: 'waitImage',
    });
    await this.customizationAlert.present();
    console.log("Download alert presented")
    this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", " Download alert presented ")
    if(this.device.platform == "browser") {
      setTimeout(() => {
        this.getMasterDataForBrowser(downloadHistoricalInspection);
      }, 1000)
    } else {

      var temp = await this.unviredCordovaSdk.dbDelete("DWNLD_TIME_HEADER", "")
      setTimeout(() => {
        this.getMasterData(downloadHistoricalInspection);
      }, 1000)
    }
    
    await this.customizationAlert.onDidDismiss().then(async (data) => {
      this.unviredCordovaSdk.logInfo("HOME","showCustomizationAlert","customization alert dismissed")
      if((this.device.platform=='browser' && this.dataService.browserFirstLogIn) || this.dataService.firstLogIn) {
        this.unviredCordovaSdk.logInfo("HOME","showCustomizationAlert","calling downloadModelFromServer method directly since no model present for first login");
        await this.dataService.userEnabledForInsightAI()
        if(this.dataService.userEnabledForInsightAI) {
          console.log('%c downloading models started ', 'background: #050505; color: #0fe862');
          await this.alertService.present('custom-loader');
          console.log("loader presented")
          await this.visionAIService.downloadModelFromServer();
          // await this.visionAIService.downloadAllAIModelsFromServer();
          if(this.alertService.isLoading) {
            console.log("loader dismissed")
            await this.alertService.dismiss();
          }
          
          // if(this.customizationAlert){
          //   this.customizationAlert.dismiss();
          // }
        }
      } else {
        this.unviredCordovaSdk.logInfo("HOME","showCustomizationAlert","calling checkAndDownloadLatestMlModel method for first time login onwards")
        await this.checkAndDownloadLatestMlModel();
      }
      // if(this.alertService.isLoading) {
      //   await this.alertService.dismiss();
      // }
      this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", "Download alert dismissed")
      if(this.device.platform == "Android") {
        this.showResourceDownloadAlert()
      } else {
        if(this.navigatedBack == false) {
          this.showUserPreferenceAlert()
        }
      }
      this.dataService.firstLogIn = false;
      this.dataService.browserFirstLogIn = false;
    })
    
  }

  async presentPopover() {
    const popover = await this.popoverController.create({
      component: HomePopupPage,
      componentProps: { page: this, insightAiEnabled: this.dataService.isUserEnabledForInsightAI, openedPage: 'home' },
      event,
      showBackdrop: true,
      animated: true
    });
    await popover.present();
    popover.onDidDismiss().then(async (data) => {
      switch (data.data) {
        case 0:
          if(navigator.onLine) {
            this.getMasterDataMenu();
          } else {
            await this.alertService.showAlert("Info","You are not connected to the internet. Please connect and try again")
          }
          // this.presentToast();
          break;
        case 1:
          this.route.navigate(['settings']);
          // this.presentSettingToast()
          // this.unviredCordovaSdk.getMessages();
          // this.unviredCordovaSdk.sendLogToServer();
          break;
          case 2:
            if(navigator.onLine) {
              await this.alertService.present('custom-loader');
              await this.checkAndDownloadLatestMlModel();
              // await this.visionAIService.downloadAllAIModelsFromServer();
              if(this.alertService.isLoading) {
                await this.alertService.dismiss();
              }
            } else {
              await this.alertService.showAlert("Info","You are not connected to the internet. Please connect and try again")
            }
            break;
      }
    });
  }

  async presentToastNotif(data) {
    this.unviredCordovaSdk.logInfo("Home", "preasenting toast", data)
    const toast = await this.toastController.create({
      message: data,
      duration: 5000,
      position: 'middle',
      color: 'dark'
    });
    toast.present();
  }

  async presentToast() {
    const toast = await this.toastController.create({
      message: this.translate.instant("Refreshing master data"),
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  async presentSettingToast() {
    const toast = await this.toastController.create({
      message: this.translate.instant("To be implemented"),
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  async showAlert(title, message) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  async checkAndDownloadData() {
    var result = await this.unviredCordovaSdk.dbSelect("DWNLD_TIME_HEADER", "")
    if (result.type == ResultType.success) {
      if (result.data.length > 0) {
        return;
      } else {
        var inBoxCount = await this.unviredCordovaSdk.inBoxItemCount()
        console.log(parseInt(inBoxCount))
        if(this.dataService.firstLogIn != true) {
          if(parseInt(inBoxCount) == 0) {
            this.dataService.firstLogIn = true;
            this.showCustomizationDownloadAlert();
          } else {
            this.showProcessingAlert()
          }
        }
      }
    }
  }

  async checkAndDownloadDataForBrowser() {
    setTimeout(async () => {
      var result = await this.unviredCordovaSdk.dbSelect("DWNLD_TIME_HEADER", "")
    if (result.type == ResultType.success) {
      if (result.data.length > 0) {
        return;
      } else {
        if(this.dataService.browserFirstLogIn == true) {
          this.dataService.browserFirstLogIn = true;
          this.showCustomizationAlert(true);
        } else {
          this.dataService.browserFirstLogIn = true;
          this.showCustomizationDownloadAlert();
        }
      }
    } else {
      if(this.dataService.browserFirstLogIn == true) {
        this.dataService.browserFirstLogIn = true;
        this.showCustomizationAlert(true);
      } else {
        this.dataService.browserFirstLogIn = true;
        this.showCustomizationDownloadAlert();
      }
    }
      
    }, 500);
  }
  
  async showResourceDownloadLoading() {
    this.customizationAlert = await this.alertController.create({
      header: 'Alert',
      message: '<div><div class="imageStyle"><img src="./assets/img/blue-hourglass.gif"></div>' + 'Please wait while we configure resources. Do not exit this screen.</div>',
      backdropDismiss: false,
      cssClass: 'waitImage',
    });
    await this.customizationAlert.present();
    console.log("Download alert presented")
    this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", " Download alert presented ")
    await this.customizationAlert.onDidDismiss().then((data) => {   
      this.showUserPreferenceAlert();     
    })
  }

  async showResourceDownloadAlert() {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'Do you want download resource files?',
      buttons: [
        {
          text: this.translate.instant('Later'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            this.showUserPreferenceAlert();

          }
        }, {
          text: this.translate.instant('Yes'),
          handler: async () => {
            this.dowloadResources();
            // this.route.navigate(['user-preferences']);
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
  }
  

  async dowloadResources() {
    this.showResourceDownloadLoading();
    // const url = 'https://transfer.unvired.io/GQ3Ni/samson%20pdf.zip';
    var tempUser = await this.unviredCordovaSdk.userSettings();
    var url = "https://sandbox.unvired.io/samson/resources.zip"
    var tempzSettings = tempUser.data["SERVER_URL"]
    if(tempzSettings.indexOf('/UMP/') > 0) {
      url = tempzSettings.replace('/UMP/','/samson/resources.zip')
    } else {
      url = tempzSettings.replace('/UMP','/samson/resources.zip')
    }
    this.unviredCordovaSdk.logInfo("Resources", "downloadResources", 'Download URL' + url);
    this.unviredCordovaSdk.logInfo("Resources", "downloadResources", 'Target path' + this.file.dataDirectory);
    const filePath = this.file.dataDirectory + 'dummy.zip';
    this.nativeHTTP.downloadFile(url, {}, {}, filePath).then((entry) => {      
      this.unviredCordovaSdk.logInfo("home", "downloadResources", 'download complete: ' + entry.toURL());
      this.file.checkFile(this.file.dataDirectory, "dummy.zip")
        .then(() => {          
          // this.customizationAlert.dismiss();
          this.zip.unzip(this.file.dataDirectory + 'dummy.zip', this.file.dataDirectory, (progress) => this.unviredCordovaSdk.logInfo("home", "downloadResources", 'Unzipping, ' + Math.round((progress.loaded / progress.total) * 100) + '%'))
          .then((result) => {            
            if(result === 0)  {
              this.unviredCordovaSdk.logInfo("Resource", "downloadResources",'SUCCESS');
            }
            if(result === -1) {
              this.unviredCordovaSdk.logInfo("Resource", "downloadResources",'FAILED');
            }
          });  
          this.customizationAlert.dismiss();
        })
        .catch((err) => {
          this.customizationAlert.dismiss();
          this.showAlert("", "Error : " + JSON.stringify(err)); 
        });
    }, (error) => {
      this.customizationAlert.dismiss();
      console.log('download error: ');
      this.showAlert("", "Error : " + JSON.stringify(error)); 
    });
  }

  async showUserPreferenceAlert() {
    const alert = await this.alertController.create({
      header: 'Alert',
      message: 'Do you want to set the user preferences?',
      buttons: [
        {
          text: this.translate.instant('Later'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
            this.dataService.checkAndSetDefaultAccount();
            this.dataService.setConfigurationOption()
            this.dataService.setLastUsedAccount();
            this.dataService.setLastUsedAsset();
            this.dataService.setLastUsedWorkOrder();

          }
        }, {
          text: this.translate.instant('Yes'),
          handler: async () => {
            this.route.navigate(['user-preferences']);
          }
        }
      ],
      backdropDismiss: false
    });
    await alert.present();
  }

  async showCustomizationDownloadAlert() {
    this.customizationAlert = await this.alertController.create({
      header: 'Alert',
      message: 'Data not downloaded. Do you want to retry?',
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {

          }
        }, {
          text: this.translate.instant('Yes'),
          handler: async () => {
            var temp = await this.unviredCordovaSdk.dbDelete("DWNLD_TIME_HEADER", "")
            this.showCustomizationAlert(true);

          }
        }
      ],
      backdropDismiss: false
    });
    await this.customizationAlert.present();
    console.log("Download alert presented")
    this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", " Download alert presented ")
    await this.customizationAlert.onDidDismiss().then(async (data) => {
      console.log("Download alert dismissed")
      // * have to include here also
      this.unviredCordovaSdk.logDebug("home", "ionViewWillEnter", "Download alert dismissed")
      this.dataService.firstLogIn = false;
    })
  }

  static async readExternalFile(filePath: string): Promise<ArrayBuffer> {
        return new Promise((resolve, reject) => {
            const fileName = filePath.substring(filePath.lastIndexOf('\\') + 1);
            (<any>window).resolveLocalFileSystemURL(filePath, (fileEntry) => {
                // Cast fileEntry to FileEntry to access the .file() method
                (fileEntry as any).file((file) => {
                    const reader = new FileReader();
                    reader.onloadend = function() {
                        console.log(fileName, "readExternalFile", "Read completed");
                        resolve(this.result as ArrayBuffer);
                    };
                    reader.onerror = function(err) {
                        console.error(fileName, "readExternalFile", "Read error: " + err);
                        reject(err);
                    };
                    reader.readAsArrayBuffer(file);
                },
                (error) => {
                    console.error(fileName, "readExternalFile", "Get file error: " + error);
                    reject(error);
                });
            },
            (error) => {
                console.error(fileName, "readExternalFile", "Resolve file system URL error: " + error);
                reject(error);
            });
        });
    }

  // Instance method that can be called from the template
  async handleReadExternalFile() {
    try {
      const fileData = await HomePage.readExternalFile("C:\\Users\\<USER>\\Downloads\\images.jpg");
      console.log('File read successfully', fileData);
      // Process the file data as needed
      this.inspection();
      // Then navigate to inspection
      
    } catch (error) {
      console.error('Error reading file:', error);
      // Still navigate to inspection even if file reading fails
      this.inspection();
    }
  }

    // Add this method to check if the plugin is available
  checkPluginAvailability() {
    if (typeof cordova !== 'undefined' && cordova.plugins && cordova.plugins.fileOpener2) {
      console.log('FileOpener2 plugin is available');
      return true;
    } else {
      console.error('FileOpener2 plugin is NOT available');
      return false;
    }
  }

  // Modify your method to use the plugin directly if available
  async openFileWithPlugin(filePath: string, mimeType: string = 'application/pdf') {
    if (!this.checkPluginAvailability()) {
      console.log('FileOpener2 plugin is not available');
      return;
    }
    
    try {
      await cordova.plugins.fileOpener2.open(
        filePath,
        mimeType,
        {
          error: (e) => {
            console.error('Error opening file', e);
            console.log('Error opening file: ' + e.message);
          },
          success: () => {
            console.log('File opened successfully');
            console.log('File opened successfully');
          }
        }
      );
    } catch (error) {
      console.error('Error opening file', error);
      console.log('Error opening file: ' + error);
    }
  }
}
